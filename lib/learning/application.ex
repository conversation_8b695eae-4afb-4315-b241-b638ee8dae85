defmodule Learning.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      LearningWeb.Telemetry,
      Learning.Repo,
      {DNSCluster, query: Application.get_env(:learning, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: Learning.PubSub},
      # Start the Finch HTTP client for sending emails
      {<PERSON>, name: Learning.Finch},
      # Start a worker by calling: Learning.Worker.start_link(arg)
      # {Learning.Worker, arg},
      # Start to serve requests, typically the last entry
      LearningWeb.Endpoint
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: Learning.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    LearningWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
